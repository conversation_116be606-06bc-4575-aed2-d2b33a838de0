<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create New Message Schedule</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .form-section {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .section-header {
            background-color: #e9ecef;
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
            margin: 0;
        }
        .section-content {
            padding: 15px;
        }
        .table-container {
            max-height: 300px;
            overflow-y: auto;
        }
        .btn-save {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        .organization-tree {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 10px;
            background-color: white;
        }
        .tree-item {
            margin-left: 20px;
        }
        .checkbox-tree input[type="checkbox"] {
            margin-right: 8px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="mb-0">Create New Message Schedule</h4>
            <div>
                <button type="button" class="btn btn-primary btn-save me-2">Save</button>
                <button type="button" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle"></i>
                </button>
            </div>
        </div>

        <!-- Basic Information Section -->
        <div class="form-section">
            <div class="section-header">Basic Information</div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">* Title</label>
                        <input type="text" class="form-control" value="Thông báo CTXH Đoàn Hưng 6.2025">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">* Message Template</label>
                        <input type="text" class="form-control" value="Thông báo CTXH 6 tháng">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">* Sending Date</label>
                        <input type="datetime-local" class="form-control" value="2025-07-04T10:00">
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Parameters Setting Section -->
        <div class="form-section">
            <div class="section-header">Message Parameters Setting</div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Message Format</label>
                        <textarea class="form-control" rows="8" placeholder="Xin chào <customer_name>, mã KH <customer_code>..."></textarea>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Sample</label>
                        <textarea class="form-control" rows="8" readonly>Xin chào Tạo Ngọ Anh Thư, mã KH: KHCN1386.
Xin gửi lại quý khách chương trình KH Lipoviton Honey tháng 6.
Thực hiện thăng hạng 25 lần.
Tổng số tiền: 1.250.000 VNĐ.
Thời gian áp dụng: 3/6/2025 – 31/8/2025. Không áp dụng cùng
lúc với các chương trình KM khác.</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Parameters Table -->
        <div class="form-section">
            <div class="section-content">
                <div class="table-container">
                    <table class="table table-bordered table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>Parameter</th>
                                <th>Description</th>
                                <th>Data type</th>
                                <th>Data value / form mapping</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>customer_name</td>
                                <td>Tên khách hàng</td>
                                <td>Data item</td>
                                <td>outlet_name</td>
                            </tr>
                            <tr>
                                <td>customer_code</td>
                                <td>Mã khách hàng</td>
                                <td>Data item</td>
                                <td>outlet_code</td>
                            </tr>
                            <tr>
                                <td>promotion_name</td>
                                <td>Tên CTKM</td>
                                <td>Input value</td>
                                <td>KH Lipoviton Honey tháng 6</td>
                            </tr>
                            <tr>
                                <td>sell_qty_1</td>
                                <td>Số lượng bán 1</td>
                                <td>Input value</td>
                                <td>25 lần</td>
                            </tr>
                            <tr>
                                <td>free_qty_1</td>
                                <td>Số lượng KM 1</td>
                                <td>Input value</td>
                                <td>9 lần</td>
                            </tr>
                            <tr>
                                <td>sell_qty_2</td>
                                <td>Số lượng bán 2</td>
                                <td>Input value</td>
                                <td>5 tháng</td>
                            </tr>
                            <tr>
                                <td>free_qty_2</td>
                                <td>Số lượng KM 2</td>
                                <td>Input value</td>
                                <td>9 lần</td>
                            </tr>
                            <tr>
                                <td>time_period</td>
                                <td>Thời gian áp dụng</td>
                                <td>Input value</td>
                                <td>1/6/2025 – 30/6/2025</td>
                            </tr>
                            <tr>
                                <td>remarks</td>
                                <td>Ghi chú thêm</td>
                                <td>Input value</td>
                                <td>Không áp dụng cùng lúc với các chương trình KM khác</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <button type="button" class="btn btn-primary btn-sm mt-2">Preview message</button>
            </div>
        </div>

        <!-- Received Outlets Section -->
        <div class="form-section">
            <div class="section-header">Received Outlets</div>
            <div class="section-content">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="filterType" id="filterOutlets" checked>
                            <label class="form-check-label" for="filterOutlets">Filter outlets</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="filterType" id="importOutlets">
                            <label class="form-check-label" for="importOutlets">Import outlets</label>
                        </div>
                    </div>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="allOutlets">
                    <label class="form-check-label" for="allOutlets">All outlets with Zalo verified</label>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">Organization</label>
                        <select class="form-select mb-2">
                            <option>Select</option>
                        </select>
                        <div class="organization-tree checkbox-tree">
                            <div>
                                <input type="checkbox" id="north1"> <label for="north1">North 1</label>
                                <div class="tree-item">
                                    <input type="checkbox" id="north2"> <label for="north2">North 2</label>
                                    <div class="tree-item">
                                        <input type="checkbox" id="northCentral"> <label for="northCentral">North Central</label>
                                        <div class="tree-item">
                                            <input type="checkbox" id="dongThuong"> <label for="dongThuong">Đông Thượng</label><br>
                                            <input type="checkbox" id="dongTrung"> <label for="dongTrung">Đông Trung</label><br>
                                            <input type="checkbox" id="haiLoi"> <label for="haiLoi">Hai Loi</label><br>
                                            <input type="checkbox" id="putDuong"> <label for="putDuong">Put Duong Phoi</label><br>
                                            <input type="checkbox" id="phongDuong"> <label for="phongDuong">Phong Duong</label><br>
                                            <input type="checkbox" id="thongBinh"> <label for="thongBinh">Thong Binh Nghi Sim</label>
                                        </div>
                                    </div>
                                    <div class="tree-item">
                                        <input type="checkbox" id="southCentral"> <label for="southCentral">South Central</label><br>
                                        <input type="checkbox" id="highland"> <label for="highland">Highland</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-primary btn-sm me-2">Confirm</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-2">Select All</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm">Clear</button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Location</label>
                        <select class="form-select mb-2">
                            <option>Select</option>
                        </select>
                        <input type="text" class="form-control" placeholder="Greater Than Or Equal To">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Outlet Type</label>
                        <select class="form-select mb-2">
                            <option>Select</option>
                        </select>
                        <input type="text" class="form-control" placeholder="Type">
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <table class="table table-bordered table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th>Out</th>
                                    <th>Type</th>
                                    <th>Region</th>
                                    <th>Province</th>
                                    <th>Ward</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Table content will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle checkbox tree interactions
            const checkboxes = document.querySelectorAll('.checkbox-tree input[type="checkbox"]');

            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    // Handle parent-child checkbox relationships
                    const parent = this.closest('.tree-item')?.previousElementSibling;
                    const children = this.closest('div')?.querySelectorAll('.tree-item input[type="checkbox"]');

                    if (this.checked && children) {
                        children.forEach(child => child.checked = true);
                    }
                });
            });

            // Handle Select All button
            document.querySelector('.btn-outline-secondary:nth-of-type(2)').addEventListener('click', function() {
                checkboxes.forEach(checkbox => checkbox.checked = true);
            });

            // Handle Clear button
            document.querySelector('.btn-outline-secondary:nth-of-type(3)').addEventListener('click', function() {
                checkboxes.forEach(checkbox => checkbox.checked = false);
            });

            // Handle All outlets checkbox
            document.getElementById('allOutlets').addEventListener('change', function() {
                if (this.checked) {
                    checkboxes.forEach(checkbox => checkbox.checked = true);
                }
            });

            // Handle Save button
            document.querySelector('.btn-save').addEventListener('click', function() {
                alert('Lưu thành công!');
            });

            // Handle Preview message button
            document.querySelector('.btn-primary.btn-sm').addEventListener('click', function() {
                alert('Xem trước tin nhắn');
            });
        });
    </script>
</body>
</html>
